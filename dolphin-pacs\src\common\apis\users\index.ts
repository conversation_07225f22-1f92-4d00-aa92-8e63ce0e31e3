import type * as Users from "./type"
import { request } from "@/http/axios"

/** 获取当前用户信息 */
export function getCurrentUserApi() {
  return request<Users.GetCurrentUserResponseData>({
    url: "user/info",
    method: "get"
  })
}

/**
 * 获取用户列表
 * @param params 搜索参数
 * @returns 用户列表数据
 */
export function getUserListApi(params?: Users.UserSearchParams) {
  return request<Users.UserListApiResponse>({
    url: "user/list",
    method: "get",
    params
  })
}

/**
 * 搜索用户列表（支持复杂搜索和分页）
 * @param params 搜索参数
 * @returns 用户列表数据
 */
export function getUserSearchApi(params?: Users.UserSearchParams) {
  return request<Users.UserListApiResponse>({
    url: "user/search",
    method: "get",
    params
  })
}