import { IElement, IElementPosition } from '../../../../interface/Element';
import { IPreviewerDrawOption } from '../../../../interface/Previewer';
import { Draw } from '../../Draw';
export declare class Previewer {
    private container;
    private canvas;
    private draw;
    private options;
    private curElement;
    private curElementSrc;
    private previewerDrawOption;
    private curPosition;
    private eventBus;
    private imageList;
    private curShowElement;
    private imageCount;
    private imagePre;
    private imageNext;
    private resizerSelection;
    private resizerHandleList;
    private resizerImageContainer;
    private resizerImage;
    private resizerSize;
    private width;
    private height;
    private mousedownX;
    private mousedownY;
    private curHandleIndex;
    private previewerContainer;
    private previewerImage;
    constructor(draw: Draw);
    private _getElementPosition;
    private _createResizerDom;
    private _keydown;
    private _mousedown;
    private _mousemove;
    private _drawPreviewer;
    private _updateImageNavigate;
    _setPreviewerTransform(scale: number, rotate: number, x: number, y: number): void;
    private _clearPreviewer;
    _updateResizerRect(width: number, height: number): void;
    _updateResizerSizeView(width: number, height: number): void;
    render(): void;
    drawResizer(element: IElement, position?: IElementPosition | null, options?: IPreviewerDrawOption): void;
    updateResizer(element: IElement, position?: IElementPosition | null): void;
    clearResizer(): void;
}
