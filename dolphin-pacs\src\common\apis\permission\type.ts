export interface PermissionItem {
  id: number
  name: string
  code: string
  path: string
  level: number
  status: number // 1启用 0禁用
  createTime: string
  updateTime: string
  remark?: string
}

export interface PermissionSearchParams {
  name?: string
  status?: number | undefined
  pageNumber?: number
  pageSize?: number
}

export interface PermissionListResponse {
  records: PermissionItem[]
  totalRow: number
}
