import { ImageDisplay } from '../../dataset/enum/Common';
import { EditorMode, EditorZone, PageMode, PaperDirection } from '../../dataset/enum/Editor';
import { ListStyle, ListType } from '../../dataset/enum/List';
import { RowFlex } from '../../dataset/enum/Row';
import { TableBorder, TdBorder, TdSlash } from '../../dataset/enum/table/Table';
import { TitleLevel } from '../../dataset/enum/Title';
import { VerticalAlign } from '../../dataset/enum/VerticalAlign';
import { ICatalog } from '../../interface/Catalog';
import { DeepRequired } from '../../interface/Common';
import { IGetControlValueOption, IGetControlValueResult, ILocationControlOption, IRemoveControlOption, ISetControlExtensionOption, ISetControlHighlightOption, ISetControlProperties, ISetControlValueOption } from '../../interface/Control';
import { IAppendElementListOption, IDrawImagePayload, IForceUpdateOption, IGetImageOption, IGetValueOption, IPainterOption } from '../../interface/Draw';
import { IEditorData, IEditorHTML, IEditorOption, IEditorResult, IEditorText, IFocusOption, ISetValueOption, IUpdateOption } from '../../interface/Editor';
import { IDeleteElementByIdOption, IElement, IElementPosition, IGetElementByIdOption, IInsertElementListOption, IUpdateElementByIdOption } from '../../interface/Element';
import { ICopyOption, IPasteOption, IPositionContextByEventOption, IPositionContextByEventResult } from '../../interface/Event';
import { IMargin } from '../../interface/Margin';
import { IRange, RangeContext } from '../../interface/Range';
import { IReplaceOption, ISearchResultContext } from '../../interface/Search';
import { ITextDecoration } from '../../interface/Text';
import { IGetTitleValueOption, IGetTitleValueResult } from '../../interface/Title';
import { IWatermark } from '../../interface/Watermark';
import { Draw } from '../draw/Draw';
import { INavigateInfo } from '../draw/interactive/Search';
import { IGetAreaValueOption, IGetAreaValueResult, IInsertAreaOption, ISetAreaPropertiesOption } from '../../interface/Area';
import { IAreaBadge, IBadge } from '../../interface/Badge';
import { IRichtextOption } from '../../interface/Command';
export declare class CommandAdapt {
    private draw;
    private range;
    private position;
    private historyManager;
    private canvasEvent;
    private options;
    private control;
    private workerManager;
    private searchManager;
    private i18n;
    private zone;
    private tableOperate;
    constructor(draw: Draw);
    mode(payload: EditorMode): void;
    cut(): void;
    copy(payload?: ICopyOption): void;
    paste(payload?: IPasteOption): void;
    selectAll(): void;
    backspace(): void;
    setRange(startIndex: number, endIndex: number, tableId?: string, startTdIndex?: number, endTdIndex?: number, startTrIndex?: number, endTrIndex?: number): void;
    replaceRange(range: IRange): void;
    setPositionContext(range: IRange): void;
    forceUpdate(options?: IForceUpdateOption): void;
    blur(): void;
    undo(): void;
    redo(): void;
    painter(options: IPainterOption): void;
    applyPainterStyle(): void;
    format(options?: IRichtextOption): void;
    font(payload: string, options?: IRichtextOption): void;
    size(payload: number, options?: IRichtextOption): void;
    sizeAdd(options?: IRichtextOption): void;
    sizeMinus(options?: IRichtextOption): void;
    bold(options?: IRichtextOption): void;
    italic(options?: IRichtextOption): void;
    underline(textDecoration?: ITextDecoration, options?: IRichtextOption): void;
    strikeout(options?: IRichtextOption): void;
    superscript(options?: IRichtextOption): void;
    subscript(options?: IRichtextOption): void;
    color(payload: string | null, options?: IRichtextOption): void;
    highlight(payload: string | null, options?: IRichtextOption): void;
    title(payload: TitleLevel | null): void;
    list(listType: ListType | null, listStyle?: ListStyle): void;
    rowFlex(payload: RowFlex): void;
    rowMargin(payload: number): void;
    insertTable(row: number, col: number): void;
    insertTableTopRow(): void;
    insertTableBottomRow(): void;
    insertTableLeftCol(): void;
    insertTableRightCol(): void;
    deleteTableRow(): void;
    deleteTableCol(): void;
    deleteTable(): void;
    mergeTableCell(): void;
    cancelMergeTableCell(): void;
    splitVerticalTableCell(): void;
    splitHorizontalTableCell(): void;
    tableTdVerticalAlign(payload: VerticalAlign): void;
    tableBorderType(payload: TableBorder): void;
    tableBorderColor(payload: string): void;
    tableTdBorderType(payload: TdBorder): void;
    tableTdSlashType(payload: TdSlash): void;
    tableTdBackgroundColor(payload: string): void;
    tableSelectAll(): void;
    hyperlink(payload: IElement): void;
    getHyperlinkRange(): [number, number] | null;
    deleteHyperlink(): void;
    cancelHyperlink(): void;
    editHyperlink(payload: string): void;
    separator(payload: number[]): void;
    pageBreak(): void;
    addWatermark(payload: IWatermark): void;
    deleteWatermark(): void;
    image(payload: IDrawImagePayload): string | null;
    search(payload: string | null): void;
    searchNavigatePre(): void;
    searchNavigateNext(): void;
    getSearchNavigateInfo(): null | INavigateInfo;
    replace(payload: string, option?: IReplaceOption): void;
    print(): Promise<void>;
    replaceImageElement(payload: string): void;
    saveAsImageElement(): void;
    changeImageDisplay(element: IElement, display: ImageDisplay): void;
    getImage(payload?: IGetImageOption): Promise<string[]>;
    getOptions(): DeepRequired<IEditorOption>;
    getValue(options?: IGetValueOption): IEditorResult;
    getValueAsync(options?: IGetValueOption): Promise<IEditorResult>;
    getAreaValue(options?: IGetAreaValueOption): IGetAreaValueResult | null;
    getHTML(): IEditorHTML;
    getText(): IEditorText;
    getWordCount(): Promise<number>;
    getCursorPosition(): IElementPosition | null;
    getRange(): IRange;
    getRangeText(): string;
    getRangeContext(): RangeContext | null;
    getRangeRow(): IElement[] | null;
    getRangeParagraph(): IElement[] | null;
    getKeywordRangeList(payload: string): IRange[];
    getKeywordContext(payload: string): ISearchResultContext[] | null;
    pageMode(payload: PageMode): void;
    pageScale(scale: number): void;
    pageScaleRecovery(): void;
    pageScaleMinus(): void;
    pageScaleAdd(): void;
    paperSize(width: number, height: number): void;
    paperDirection(payload: PaperDirection): void;
    getPaperMargin(): number[];
    setPaperMargin(payload: IMargin): void;
    setMainBadge(payload: IBadge | null): void;
    setAreaBadge(payload: IAreaBadge[]): void;
    insertElementList(payload: IElement[], options?: IInsertElementListOption): void;
    appendElementList(elementList: IElement[], options?: IAppendElementListOption): void;
    updateElementById(payload: IUpdateElementByIdOption): void;
    deleteElementById(payload: IDeleteElementByIdOption): void;
    getElementById(payload: IGetElementByIdOption): IElement[];
    setValue(payload: Partial<IEditorData>, options?: ISetValueOption): void;
    removeControl(payload?: IRemoveControlOption): void;
    translate(path: string): string;
    setLocale(payload: string): void;
    getLocale(): string;
    getCatalog(): Promise<ICatalog | null>;
    locationCatalog(titleId: string): void;
    wordTool(): void;
    setHTML(payload: Partial<IEditorHTML>): void;
    setGroup(): string | null;
    deleteGroup(groupId: string): void;
    getGroupIds(): Promise<string[]>;
    locationGroup(groupId: string): void;
    setZone(zone: EditorZone): void;
    getControlValue(payload: IGetControlValueOption): IGetControlValueResult | null;
    setControlValue(payload: ISetControlValueOption): void;
    setControlValueList(payload: ISetControlValueOption[]): void;
    setControlExtension(payload: ISetControlExtensionOption): void;
    setControlExtensionList(payload: ISetControlExtensionOption[]): void;
    setControlProperties(payload: ISetControlProperties): void;
    setControlPropertiesList(payload: ISetControlProperties[]): void;
    setControlHighlight(payload: ISetControlHighlightOption): void;
    updateOptions(payload: IUpdateOption): void;
    getControlList(): IElement[];
    locationControl(controlId: string, options?: ILocationControlOption): void;
    insertControl(payload: IElement): void;
    getContainer(): HTMLDivElement;
    getTitleValue(payload: IGetTitleValueOption): IGetTitleValueResult | null;
    getPositionContextByEvent(evt: MouseEvent, options?: IPositionContextByEventOption): IPositionContextByEventResult | null;
    insertTitle(payload: IElement): void;
    focus(payload?: IFocusOption): void;
    insertArea(payload: IInsertAreaOption): string | null;
    setAreaProperties(payload: ISetAreaPropertiesOption): void;
    locationArea(areaId: string): void;
}
