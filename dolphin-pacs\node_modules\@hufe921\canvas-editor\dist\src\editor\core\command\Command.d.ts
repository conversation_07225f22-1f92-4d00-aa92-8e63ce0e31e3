import { CommandAdapt } from './CommandAdapt';
export declare class Command {
    executeMode: CommandAdapt['mode'];
    executeCut: CommandAdapt['cut'];
    executeCopy: CommandAdapt['copy'];
    executePaste: CommandAdapt['paste'];
    executeSelectAll: CommandAdapt['selectAll'];
    executeBackspace: CommandAdapt['backspace'];
    executeSetRange: CommandAdapt['setRange'];
    executeReplaceRange: CommandAdapt['replaceRange'];
    executeSetPositionContext: CommandAdapt['setPositionContext'];
    executeForceUpdate: CommandAdapt['forceUpdate'];
    executeBlur: CommandAdapt['blur'];
    executeUndo: CommandAdapt['undo'];
    executeRedo: CommandAdapt['redo'];
    executePainter: CommandAdapt['painter'];
    executeApplyPainterStyle: CommandAdapt['applyPainterStyle'];
    executeFormat: CommandAdapt['format'];
    executeFont: CommandAdapt['font'];
    executeSize: CommandAdapt['size'];
    executeSizeAdd: CommandAdapt['sizeAdd'];
    executeSizeMinus: CommandAdapt['sizeMinus'];
    executeBold: CommandAdapt['bold'];
    executeItalic: CommandAdapt['italic'];
    executeUnderline: CommandAdapt['underline'];
    executeStrikeout: CommandAdapt['strikeout'];
    executeSuperscript: CommandAdapt['superscript'];
    executeSubscript: CommandAdapt['subscript'];
    executeColor: CommandAdapt['color'];
    executeHighlight: CommandAdapt['highlight'];
    executeTitle: CommandAdapt['title'];
    executeList: CommandAdapt['list'];
    executeRowFlex: CommandAdapt['rowFlex'];
    executeRowMargin: CommandAdapt['rowMargin'];
    executeInsertTable: CommandAdapt['insertTable'];
    executeInsertTableTopRow: CommandAdapt['insertTableTopRow'];
    executeInsertTableBottomRow: CommandAdapt['insertTableBottomRow'];
    executeInsertTableLeftCol: CommandAdapt['insertTableLeftCol'];
    executeInsertTableRightCol: CommandAdapt['insertTableRightCol'];
    executeDeleteTableRow: CommandAdapt['deleteTableRow'];
    executeDeleteTableCol: CommandAdapt['deleteTableCol'];
    executeDeleteTable: CommandAdapt['deleteTable'];
    executeMergeTableCell: CommandAdapt['mergeTableCell'];
    executeCancelMergeTableCell: CommandAdapt['cancelMergeTableCell'];
    executeSplitVerticalTableCell: CommandAdapt['splitVerticalTableCell'];
    executeSplitHorizontalTableCell: CommandAdapt['splitHorizontalTableCell'];
    executeTableTdVerticalAlign: CommandAdapt['tableTdVerticalAlign'];
    executeTableBorderType: CommandAdapt['tableBorderType'];
    executeTableBorderColor: CommandAdapt['tableBorderColor'];
    executeTableTdBorderType: CommandAdapt['tableTdBorderType'];
    executeTableTdSlashType: CommandAdapt['tableTdSlashType'];
    executeTableTdBackgroundColor: CommandAdapt['tableTdBackgroundColor'];
    executeTableSelectAll: CommandAdapt['tableSelectAll'];
    executeImage: CommandAdapt['image'];
    executeHyperlink: CommandAdapt['hyperlink'];
    executeDeleteHyperlink: CommandAdapt['deleteHyperlink'];
    executeCancelHyperlink: CommandAdapt['cancelHyperlink'];
    executeEditHyperlink: CommandAdapt['editHyperlink'];
    executeSeparator: CommandAdapt['separator'];
    executePageBreak: CommandAdapt['pageBreak'];
    executeAddWatermark: CommandAdapt['addWatermark'];
    executeDeleteWatermark: CommandAdapt['deleteWatermark'];
    executeSearch: CommandAdapt['search'];
    executeSearchNavigatePre: CommandAdapt['searchNavigatePre'];
    executeSearchNavigateNext: CommandAdapt['searchNavigateNext'];
    executeReplace: CommandAdapt['replace'];
    executePrint: CommandAdapt['print'];
    executeReplaceImageElement: CommandAdapt['replaceImageElement'];
    executeSaveAsImageElement: CommandAdapt['saveAsImageElement'];
    executeChangeImageDisplay: CommandAdapt['changeImageDisplay'];
    executePageMode: CommandAdapt['pageMode'];
    executePageScale: CommandAdapt['pageScale'];
    executePageScaleRecovery: CommandAdapt['pageScaleRecovery'];
    executePageScaleMinus: CommandAdapt['pageScaleMinus'];
    executePageScaleAdd: CommandAdapt['pageScaleAdd'];
    executePaperSize: CommandAdapt['paperSize'];
    executePaperDirection: CommandAdapt['paperDirection'];
    executeSetPaperMargin: CommandAdapt['setPaperMargin'];
    executeSetMainBadge: CommandAdapt['setMainBadge'];
    executeSetAreaBadge: CommandAdapt['setAreaBadge'];
    executeInsertElementList: CommandAdapt['insertElementList'];
    executeInsertArea: CommandAdapt['insertArea'];
    executeSetAreaProperties: CommandAdapt['setAreaProperties'];
    executeLocationArea: CommandAdapt['locationArea'];
    executeAppendElementList: CommandAdapt['appendElementList'];
    executeUpdateElementById: CommandAdapt['updateElementById'];
    executeDeleteElementById: CommandAdapt['deleteElementById'];
    executeSetValue: CommandAdapt['setValue'];
    executeRemoveControl: CommandAdapt['removeControl'];
    executeTranslate: CommandAdapt['translate'];
    executeSetLocale: CommandAdapt['setLocale'];
    executeLocationCatalog: CommandAdapt['locationCatalog'];
    executeWordTool: CommandAdapt['wordTool'];
    executeSetHTML: CommandAdapt['setHTML'];
    executeSetGroup: CommandAdapt['setGroup'];
    executeDeleteGroup: CommandAdapt['deleteGroup'];
    executeLocationGroup: CommandAdapt['locationGroup'];
    executeSetZone: CommandAdapt['setZone'];
    executeSetControlValue: CommandAdapt['setControlValue'];
    executeSetControlValueList: CommandAdapt['setControlValueList'];
    executeSetControlExtension: CommandAdapt['setControlExtension'];
    executeSetControlExtensionList: CommandAdapt['setControlExtensionList'];
    executeSetControlProperties: CommandAdapt['setControlProperties'];
    executeSetControlPropertiesList: CommandAdapt['setControlPropertiesList'];
    executeSetControlHighlight: CommandAdapt['setControlHighlight'];
    executeLocationControl: CommandAdapt['locationControl'];
    executeInsertControl: CommandAdapt['insertControl'];
    executeUpdateOptions: CommandAdapt['updateOptions'];
    executeInsertTitle: CommandAdapt['insertTitle'];
    executeFocus: CommandAdapt['focus'];
    getCatalog: CommandAdapt['getCatalog'];
    getImage: CommandAdapt['getImage'];
    getOptions: CommandAdapt['getOptions'];
    getValue: CommandAdapt['getValue'];
    getValueAsync: CommandAdapt['getValueAsync'];
    getAreaValue: CommandAdapt['getAreaValue'];
    getHTML: CommandAdapt['getHTML'];
    getText: CommandAdapt['getText'];
    getWordCount: CommandAdapt['getWordCount'];
    getCursorPosition: CommandAdapt['getCursorPosition'];
    getRange: CommandAdapt['getRange'];
    getRangeText: CommandAdapt['getRangeText'];
    getRangeContext: CommandAdapt['getRangeContext'];
    getRangeRow: CommandAdapt['getRangeRow'];
    getRangeParagraph: CommandAdapt['getRangeParagraph'];
    getKeywordRangeList: CommandAdapt['getKeywordRangeList'];
    getKeywordContext: CommandAdapt['getKeywordContext'];
    getPaperMargin: CommandAdapt['getPaperMargin'];
    getSearchNavigateInfo: CommandAdapt['getSearchNavigateInfo'];
    getLocale: CommandAdapt['getLocale'];
    getGroupIds: CommandAdapt['getGroupIds'];
    getControlValue: CommandAdapt['getControlValue'];
    getControlList: CommandAdapt['getControlList'];
    getContainer: CommandAdapt['getContainer'];
    getTitleValue: CommandAdapt['getTitleValue'];
    getPositionContextByEvent: CommandAdapt['getPositionContextByEvent'];
    getElementById: CommandAdapt['getElementById'];
    constructor(adapt: CommandAdapt);
}
