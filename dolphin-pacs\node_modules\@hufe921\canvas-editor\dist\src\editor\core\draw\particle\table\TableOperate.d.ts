import { IElement, TableBorder, VerticalAlign } from '../../../..';
import { TdBorder, TdSlash } from '../../../../dataset/enum/table/Table';
import { Draw } from '../../Draw';
export declare class TableOperate {
    private draw;
    private range;
    private position;
    private tableTool;
    private tableParticle;
    private options;
    constructor(draw: Draw);
    insertTable(row: number, col: number): void;
    insertTableTopRow(): void;
    insertTableBottomRow(): void;
    adjustColWidth(element: IElement): void;
    insertTableLeftCol(): void;
    insertTableRightCol(): void;
    deleteTableRow(): void;
    deleteTableCol(): void;
    deleteTable(): void;
    mergeTableCell(): void;
    cancelMergeTableCell(): void;
    splitVerticalTableCell(): void;
    splitHorizontalTableCell(): void;
    tableTdVerticalAlign(payload: VerticalAlign): void;
    tableBorderType(payload: TableBorder): void;
    tableBorderColor(payload: string): void;
    tableTdBorderType(payload: TdBorder): void;
    tableTdSlashType(payload: TdSlash): void;
    tableTdBackgroundColor(payload: string): void;
    tableSelectAll(): void;
}
