export declare enum MaxHeightRatio {
    HALF = "half",
    ONE_THIRD = "one-third",
    QUARTER = "quarter"
}
export declare enum NumberType {
    ARABIC = "arabic",
    CHINESE = "chinese"
}
export declare enum ImageDisplay {
    INLINE = "inline",
    BLOCK = "block",
    SURROUND = "surround",
    FLOAT_TOP = "float-top",
    FLOAT_BOTTOM = "float-bottom"
}
export declare enum LocationPosition {
    BEFORE = "before",
    AFTER = "after",
    OUTER_BEFORE = "outer-before",
    OUTER_AFTER = "outer-after"
}
export declare enum FlexDirection {
    ROW = "row",
    COLUMN = "column"
}
