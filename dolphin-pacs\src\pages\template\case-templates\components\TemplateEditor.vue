<template>
  <div class="template-editor-page">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button
          link
          @click="goBack"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回模板列表
        </el-button>
        <div class="template-title">
          {{ templateName || '自定义报告编辑器' }}
        </div>
      </div>

      <!-- 编辑工具栏 -->
      <div class="toolbar-center">
        <el-button-group>
          <el-button @click="insertText" size="small">
            <el-icon><EditPen /></el-icon>
            文本
          </el-button>
          <el-button @click="insertTable" size="small">
            <el-icon><Grid /></el-icon>
            表格
          </el-button>
          <el-button @click="insertImage" size="small">
            <el-icon><Picture /></el-icon>
            图片
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button @click="setBold" size="small" :type="formatState.bold ? 'primary' : 'default'">
            <strong>B</strong>
          </el-button>
          <el-button @click="setItalic" size="small" :type="formatState.italic ? 'primary' : 'default'">
            <em>I</em>
          </el-button>
          <el-button @click="setUnderline" size="small" :type="formatState.underline ? 'primary' : 'default'">
            <u>U</u>
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-select v-model="currentFontSize" @change="setFontSize" size="small" style="width: 80px">
          <el-option v-for="size in fontSizes" :key="size" :label="size" :value="size" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button @click="handleSave" type="primary" size="small">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button @click="handlePrint" size="small">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport" size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container">
      <div id="canvas-editor" class="canvas-editor-wrapper"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Document, Printer, Download, EditPen, Grid, Picture } from '@element-plus/icons-vue'
import CanvasEditor, { EditorMode } from '@hufe921/canvas-editor'

defineOptions({
  name: "TemplateEditor"
})

const route = useRoute()
const router = useRouter()

// 模板信息
const templateId = ref<string>(route.params.id as string)
const templateName = ref<string>(route.query.name as string)

// 编辑器实例
let editorInstance: any = null

// 编辑器状态
const formatState = ref({
  bold: false,
  italic: false,
  underline: false
})

const currentFontSize = ref(14)
const fontSizes = [10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48]

// 获取空白文档内容
const getBlankDocument = () => {
  return {
    main: [
      {
        value: '点击此处开始编辑文档...\n',
        size: 14,
        color: '#999999'
      }
    ]
  }
}

// 初始化编辑器
const initEditor = () => {
  try {
    const container = document.getElementById('canvas-editor') as HTMLDivElement
    if (!container) return

    // 获取空白文档内容
    const documentContent = getBlankDocument()

    // 初始化编辑器
    editorInstance = new CanvasEditor(container, documentContent, {
      height: 800,
      mode: EditorMode.EDIT,
      header: {
        disabled: false,
        top: 50
      },
      footer: {
        disabled: false,
        bottom: 50
      },
      margins: [100, 120, 100, 120],
      watermark: {
        data: '医院超声科',
        opacity: 0.1,
        size: 100,
        color: '#cccccc'
      }
    })

    // 监听编辑器事件
    setupEditorEvents()

    ElMessage.success('编辑器加载成功')
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    ElMessage.error('编辑器初始化失败')
    // 如果初始化失败，创建模拟编辑器
    createMockEditor()
  }
}

// 设置编辑器事件监听
const setupEditorEvents = () => {
  if (!editorInstance) return

  // 监听选择变化，更新格式状态
  editorInstance.listener.rangeStyleChange = (payload: any) => {
    formatState.value.bold = payload.bold || false
    formatState.value.italic = payload.italic || false
    formatState.value.underline = payload.underline || false
    currentFontSize.value = payload.size || 14
  }
}

// 编辑器功能方法
const insertText = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 插入文本块
  editorInstance.command.executeInsertElementList([
    {
      value: '新文本块',
      size: 14
    }
  ])
}

const insertTable = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 插入3x3表格
  editorInstance.command.executeInsertTable(3, 3)
  ElMessage.success('表格插入成功')
}

const insertImage = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 创建文件输入
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event: any) => {
        editorInstance.command.executeInsertElementList([
          {
            type: 'image',
            value: event.target.result,
            width: 200,
            height: 150
          }
        ])
        ElMessage.success('图片插入成功')
      }
      reader.readAsDataURL(file)
    }
  }
  input.click()
}

const setBold = () => {
  if (!editorInstance) return
  editorInstance.command.executeBold()
}

const setItalic = () => {
  if (!editorInstance) return
  editorInstance.command.executeItalic()
}

const setUnderline = () => {
  if (!editorInstance) return
  editorInstance.command.executeUnderline()
}

const setFontSize = (size: number) => {
  if (!editorInstance) return
  editorInstance.command.executeSize(size)
}

// 返回模板列表
const goBack = () => {
  ElMessageBox.confirm(
    '确定要离开编辑器吗？未保存的更改将会丢失。',
    '确认离开',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    router.go(-1)
  }).catch(() => {
    // 用户取消
  })
}

// 保存文档
const handleSave = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  try {
    const content = editorInstance.getValue()
    // 这里可以发送请求保存到服务器
    console.log('保存内容:', content)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 打印文档
const handlePrint = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  try {
    editorInstance.print()
    ElMessage.success('准备打印')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印功能暂不可用')
  }
}

// 导出文档
const handleExport = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  ElMessageBox.confirm(
    '选择导出格式',
    '导出文档',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: 'PDF',
      cancelButtonText: 'Word',
      type: 'info'
    }
  ).then(() => {
    // 导出 PDF
    try {
      editorInstance.exportPDF(`${templateName.value}.pdf`)
      ElMessage.success('PDF 导出成功')
    } catch (error) {
      ElMessage.error('PDF 导出失败')
    }
  }).catch((action: string) => {
    if (action === 'cancel') {
      // 导出 Word
      try {
        editorInstance.exportWord(`${templateName.value}.docx`)
        ElMessage.success('Word 导出成功')
      } catch (error) {
        ElMessage.error('Word 导出失败')
      }
    }
  })
}

// 生命周期
onMounted(() => {
  // 延迟初始化编辑器，确保DOM已渲染
  setTimeout(initEditor, 100)
})

// 创建模拟编辑器（当真实库不可用时）
const createMockEditor = () => {
  const container = document.getElementById('canvas-editor')
  if (!container) return

  container.innerHTML = `
    <div style="
      width: 100%;
      height: 800px;
      border: 1px solid #ddd;
      background: white;
      padding: 50px;
      box-sizing: border-box;
      overflow-y: auto;
      font-family: 'Microsoft YaHei', sans-serif;
    ">
      <div style="text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 30px;">
        ${templateName.value || '超声检查报告'}
      </div>
      <div contenteditable="true" style="min-height: 600px; line-height: 1.8; font-size: 14px;">
        <p><strong>患者姓名：</strong>___________　　<strong>性别：</strong>_____　　<strong>年龄：</strong>_____</p>
        <p><strong>检查日期：</strong>___________　　<strong>报告医师：</strong>___________</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【检查所见】</strong></p>
        <p>请在此处填写详细的检查所见...</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【超声提示】</strong></p>
        <p>请在此处填写超声检查提示...</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【建议】</strong></p>
        <p>请在此处填写医学建议...</p>
      </div>
    </div>
  `

  // 模拟编辑器实例
  editorInstance = {
    getValue: () => {
      const editableDiv = container.querySelector('[contenteditable="true"]')
      return editableDiv ? editableDiv.innerHTML : ''
    },
    print: () => window.print(),
    exportPDF: (filename: string) => {
      console.log('导出 PDF:', filename)
    },
    exportWord: (filename: string) => {
      console.log('导出 Word:', filename)
    }
  }

  ElMessage.info('使用模拟编辑器，功能有限')
}

onUnmounted(() => {
  // 清理编辑器实例
  if (editorInstance && typeof editorInstance.destroy === 'function') {
    editorInstance.destroy()
  }
})
</script>

<style lang="scss" scoped>
.template-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  gap: 16px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;

  .back-btn {
    color: #409eff;
    font-size: 14px;
    padding: 8px 0;

    &:hover {
      color: #66b1ff;
    }
  }

  .template-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.toolbar-right {
  display: flex;
  gap: 8px;

  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.editor-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.canvas-editor-wrapper {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 响应式布局
@media screen and (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .toolbar-center {
    flex-wrap: wrap;
    gap: 8px;
  }

  .editor-container {
    padding: 10px;
  }

  .canvas-editor-wrapper {
    max-width: 100%;
  }
}
</style>