<template>
  <div class="template-editor-page">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button
          type="text"
          @click="goBack"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回模板列表
        </el-button>
        <div class="template-title">
          {{ templateName || '报告模板编辑' }}
        </div>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="handleSave" type="primary" size="small">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button @click="handlePrint" size="small">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport" size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container">
      <div id="canvas-editor" class="canvas-editor-wrapper"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Document, Printer, Download } from '@element-plus/icons-vue'
import CanvasEditor, { EditorMode } from '@hufe921/canvas-editor'

defineOptions({
  name: "TemplateEditor"
})

const route = useRoute()
const router = useRouter()

// 模板信息
const templateId = ref<string>(route.params.id as string)
const templateName = ref<string>(route.query.name as string)

// 编辑器实例
let editorInstance: any = null

// 获取模板初始内容
const getTemplateContent = (id: string) => {
  // 根据不同模板ID返回不同的初始内容
  const templates: Record<string, any> = {
    '1': {
      // 心脏超声检查报告模板
      main: [
        {
          value: '心脏超声检查报告\n',
          size: 20,
          bold: true,
          color: '#333333',
          rowFlex: 'center'
        },
        {
          value: '\n患者姓名：___________    性别：_____    年龄：_____\n',
          size: 14
        },
        {
          value: '检查日期：___________    报告医师：___________\n\n',
          size: 14
        },
        {
          value: '【检查所见】\n',
          size: 16,
          bold: true,
          color: '#2c5aa0'
        },
        {
          value: '1. 心脏位置：___________\n',
          size: 14
        },
        {
          value: '2. 心房心室：___________\n',
          size: 14
        },
        {
          value: '3. 心脏瓣膜：___________\n',
          size: 14
        },
        {
          value: '4. 心肌壁运动：___________\n\n',
          size: 14
        },
        {
          value: '【超声提示】\n',
          size: 16,
          bold: true,
          color: '#2c5aa0'
        },
        {
          value: '___________\n\n',
          size: 14
        },
        {
          value: '【建议】\n',
          size: 16,
          bold: true,
          color: '#2c5aa0'
        },
        {
          value: '___________\n',
          size: 14
        }
      ]
    },
    '2': {
      // 腹部超声检查报告模板
      main: [
        {
          value: '腹部超声检查报告\n',
          size: 20,
          bold: true,
          color: '#333333',
          rowFlex: 'center'
        },
        {
          value: '\n患者姓名：___________    性别：_____    年龄：_____\n',
          size: 14
        },
        {
          value: '检查日期：___________    报告医师：___________\n\n',
          size: 14
        },
        {
          value: '【检查所见】\n',
          size: 16,
          bold: true,
          color: '#2c5aa0'
        },
        {
          value: '肝脏：___________\n',
          size: 14
        },
        {
          value: '胆囊：___________\n',
          size: 14
        },
        {
          value: '胰腺：___________\n',
          size: 14
        },
        {
          value: '脾脏：___________\n',
          size: 14
        },
        {
          value: '双肾：___________\n\n',
          size: 14
        },
        {
          value: '【超声提示】\n',
          size: 16,
          bold: true,
          color: '#2c5aa0'
        },
        {
          value: '___________\n',
          size: 14
        }
      ]
    },
    // 其他模板的默认内容
    default: {
      main: [
        {
          value: '超声检查报告\n',
          size: 20,
          bold: true,
          color: '#333333',
          rowFlex: 'center'
        },
        {
          value: '\n患者信息：\n',
          size: 16,
          bold: true
        },
        {
          value: '姓名：___________    性别：_____    年龄：_____\n',
          size: 14
        },
        {
          value: '检查日期：___________\n\n',
          size: 14
        },
        {
          value: '检查内容：\n',
          size: 16,
          bold: true
        },
        {
          value: '请在此处填写检查内容...\n\n',
          size: 14,
          color: '#666666'
        },
        {
          value: '检查结果：\n',
          size: 16,
          bold: true
        },
        {
          value: '请在此处填写检查结果...\n',
          size: 14,
          color: '#666666'
        }
      ]
    }
  }

  return templates[id] || templates.default
}

// 初始化编辑器
const initEditor = () => {
  try {
    const container = document.getElementById('canvas-editor') as HTMLDivElement
    if (!container) return

    // 获取模板内容
    const templateContent = getTemplateContent(templateId.value)

    // 初始化编辑器
    editorInstance = new CanvasEditor(container, templateContent, {
      height: 800,
      mode: 'edit' as any,
      header: {
        disabled: false,
        top: 50
      },
      footer: {
        disabled: false,
        bottom: 50
      },
      margins: [100, 120, 100, 120],
      watermark: {
        data: '医院超声科',
        opacity: 0.1,
        size: 100,
        color: '#cccccc'
      }
    })

    ElMessage.success('编辑器加载成功')
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    ElMessage.error('编辑器初始化失败')
    // 如果初始化失败，创建模拟编辑器
    createMockEditor()
  }
}

// 返回模板列表
const goBack = () => {
  ElMessageBox.confirm(
    '确定要离开编辑器吗？未保存的更改将会丢失。',
    '确认离开',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    router.go(-1)
  }).catch(() => {
    // 用户取消
  })
}

// 保存文档
const handleSave = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  try {
    const content = editorInstance.getValue()
    // 这里可以发送请求保存到服务器
    console.log('保存内容:', content)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 打印文档
const handlePrint = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  try {
    editorInstance.print()
    ElMessage.success('准备打印')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印功能暂不可用')
  }
}

// 导出文档
const handleExport = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  ElMessageBox.confirm(
    '选择导出格式',
    '导出文档',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: 'PDF',
      cancelButtonText: 'Word',
      type: 'info'
    }
  ).then(() => {
    // 导出 PDF
    try {
      editorInstance.exportPDF(`${templateName.value}.pdf`)
      ElMessage.success('PDF 导出成功')
    } catch (error) {
      ElMessage.error('PDF 导出失败')
    }
  }).catch((action: string) => {
    if (action === 'cancel') {
      // 导出 Word
      try {
        editorInstance.exportWord(`${templateName.value}.docx`)
        ElMessage.success('Word 导出成功')
      } catch (error) {
        ElMessage.error('Word 导出失败')
      }
    }
  })
}

// 生命周期
onMounted(() => {
  // 延迟初始化编辑器，确保DOM已渲染
  setTimeout(initEditor, 100)
})

// 创建模拟编辑器（当真实库不可用时）
const createMockEditor = () => {
  const container = document.getElementById('canvas-editor')
  if (!container) return

  container.innerHTML = `
    <div style="
      width: 100%;
      height: 800px;
      border: 1px solid #ddd;
      background: white;
      padding: 50px;
      box-sizing: border-box;
      overflow-y: auto;
      font-family: 'Microsoft YaHei', sans-serif;
    ">
      <div style="text-align: center; font-size: 24px; font-weight: bold; margin-bottom: 30px;">
        ${templateName.value || '超声检查报告'}
      </div>
      <div contenteditable="true" style="min-height: 600px; line-height: 1.8; font-size: 14px;">
        <p><strong>患者姓名：</strong>___________　　<strong>性别：</strong>_____　　<strong>年龄：</strong>_____</p>
        <p><strong>检查日期：</strong>___________　　<strong>报告医师：</strong>___________</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【检查所见】</strong></p>
        <p>请在此处填写详细的检查所见...</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【超声提示】</strong></p>
        <p>请在此处填写超声检查提示...</p>
        <br>
        <p><strong style="color: #2c5aa0; font-size: 16px;">【建议】</strong></p>
        <p>请在此处填写医学建议...</p>
      </div>
    </div>
  `

  // 模拟编辑器实例
  editorInstance = {
    getValue: () => {
      const editableDiv = container.querySelector('[contenteditable="true"]')
      return editableDiv ? editableDiv.innerHTML : ''
    },
    print: () => window.print(),
    exportPDF: (filename: string) => {
      console.log('导出 PDF:', filename)
    },
    exportWord: (filename: string) => {
      console.log('导出 Word:', filename)
    }
  }

  ElMessage.info('使用模拟编辑器，功能有限')
}

onUnmounted(() => {
  // 清理编辑器实例
  if (editorInstance && typeof editorInstance.destroy === 'function') {
    editorInstance.destroy()
  }
})
</script>

<style lang="scss" scoped>
.template-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;

  .back-btn {
    color: #409eff;
    font-size: 14px;
    padding: 8px 0;

    &:hover {
      color: #66b1ff;
    }
  }

  .template-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.toolbar-right {
  display: flex;
  gap: 8px;

  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.editor-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.canvas-editor-wrapper {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 响应式布局
@media screen and (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .editor-container {
    padding: 10px;
  }

  .canvas-editor-wrapper {
    max-width: 100%;
  }
}
</style>