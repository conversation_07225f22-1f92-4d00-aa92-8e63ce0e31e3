<script lang="ts" setup>
import { ref, reactive, onMounted, useTemplateRef } from "vue";
import { ElMessage } from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type {
  UserManagementInfo,
  UserSearchParams,
} from "@/common/apis/users/type";
import type { RoleInfo } from "@/common/apis/roles/type";
import { Search, Refresh } from "@element-plus/icons-vue";
import { getUserListApi, getUserSearchApi } from "@/common/apis/users";
import {
  getRoleListApi,
  getUserRoleListApi,
  bindUserRoleApi,
} from "@/common/apis/roles";

defineOptions({
  // 命名当前组件
  name: "UserManagement",
});

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 搜索表单数据
const searchForm = reactive<UserSearchParams>({
  username: "",
  nickname: "",
  phone: "",
  email: "",
  pageNumber: 1,
  pageSize: 10,
});

// 表格数据
const tableData = ref<UserManagementInfo[]>([]);
// 分页信息
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
});

const currentUser = ref<UserManagementInfo | null>(null);
const currentUserRoles = ref<RoleInfo[]>([]);
const allRoles = ref<RoleInfo[]>([]);

// 绑定角色相关状态
const bindRoleDialogVisible = ref(false);
const bindRoleLoading = ref(false);
const selectedRoleId = ref<number | undefined>(undefined);

// 检查角色是否已被用户拥有
const isRoleDisabled = (roleId: number) => {
  return currentUserRoles.value.some((userRole) => userRole.id === roleId);
};

// 获取当前角色的占位符文本
const getCurrentRolesPlaceholder = () => {
  if (currentUserRoles.value.length > 0) {
    const roleNames = currentUserRoles.value
      .map((role: RoleInfo) => `${role.name} (${role.code})`)
      .join("、");
    return `${roleNames} `;
  }
  return "暂无分配角色 - 请选择要绑定的角色";
};

// 表格配置
const xGridOpt = reactive<VxeGridProps>({
  border: true,
  stripe: true,
  resizable: true,
  showHeaderOverflow: true,
  showOverflow: true,
  keepSource: true,
  id: "UserManagement",
  height: 600,
  loading: false,
  data: tableData as any,
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["username"].includes(column.field),
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: 50,
    },
    {
      type: "seq",
      width: 70,
      title: "序号",
    },
    {
      field: "id",
      title: "用户编号",
      width: 100,
    },
    {
      field: "username",
      title: "用户名称",
      minWidth: 120,
    },
    {
      field: "nickname",
      title: "用户昵称",
      minWidth: 120,
    },
    {
      field: "gender",
      title: "性别",
      width: 80,
      slots: { default: "gender-column" },
    },
    {
      field: "phone",
      title: "手机号码",
      width: 140,
    },
    {
      field: "email",
      title: "邮箱",
      minWidth: 180,
      showOverflow: "tooltip",
    },
    {
      title: "操作",
      width: 120,
      fixed: "right",
      slots: { default: "action-column" },
    },
  ],
});

// 用户数据加载函数
const loadUserData = async (resetPage = false) => {
  try {
    xGridOpt.loading = true;

    // 如果需要重置页码（搜索时）
    if (resetPage) {
      pagination.currentPage = 1;
    }

    const params: UserSearchParams = {
      username: searchForm.username || undefined,
      nickname: searchForm.nickname || undefined,
      phone: searchForm.phone || undefined,
      email: searchForm.email || undefined,
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize,
    };

    const response = await getUserSearchApi(params);

    // 打印响应数据结构用于调试
    console.log("用户API响应:", response);

    // 检查响应数据结构
    let userData: UserManagementInfo[] = [];
    let total = 0;

    if (response && response.data) {
      // 如果返回的是分页数据结构
      if (typeof response.data === 'object' && 'records' in response.data && Array.isArray(response.data.records)) {
        userData = response.data.records;
        total = response.data.total || 0;
      }
      // 如果返回的是数组（兼容旧格式）
      else if (Array.isArray(response.data)) {
        userData = response.data;
        total = userData.length;
      }
      // 如果返回的是其他格式，尝试获取列表数据
      else if (typeof response.data === 'object' && 'list' in response.data && Array.isArray(response.data.list)) {
        userData = response.data.list;
        total = (response.data as any).total || (response.data as any).count || userData.length;
      }
    }

    // 更新表格数据和分页信息
    tableData.value = userData;
    pagination.total = total;

    console.log("用户数据:", userData);
    console.log("总数:", total);

    xGridOpt.loading = false;
  } catch (error) {
    console.error("获取用户数据失败:", error);
    ElMessage.error("获取用户数据失败，请稍后重试");
    tableData.value = [];
    pagination.total = 0;
    xGridOpt.loading = false;
  }
};

// 查询数据
const handleQuery = () => {
  loadUserData(true); // 搜索时重置到第一页
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: "",
    nickname: "",
    phone: "",
    email: "",
  });
  loadUserData(true); // 重置时回到第一页
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1; // 改变页大小时回到第一页
  loadUserData();
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  loadUserData();
};

// 获取性别标签类型
const getGenderTagType = (gender: string) => {
  switch (gender) {
    case "男":
      return "primary";
    case "女":
      return "danger";
    default:
      return "info";
  }
};

// 新增用户
const handleAdd = () => {
  ElMessage.info("新增用户功能待实现");
};

// 批量删除
const handleBatchDelete = () => {
  const selectRecords = xGridDom.value?.getCheckboxRecords();
  if (!selectRecords || selectRecords.length === 0) {
    ElMessage.warning("请选择要删除的用户");
    return;
  }
  ElMessage.info("批量删除功能待实现");
};

// 绑定角色
const handleBindRole = async (row: UserManagementInfo) => {
  try {
    currentUser.value = row;
    bindRoleDialogVisible.value = true;
    bindRoleLoading.value = true;

    // 同时调用两个API
    const [userRolesResponse, allRolesResponse] = await Promise.all([
      getUserRoleListApi({ userId: row.id }),
      getRoleListApi({}),
    ]);

    // 处理用户当前角色
    currentUserRoles.value = Array.isArray(userRolesResponse.data)
      ? userRolesResponse.data
      : [];

    // 处理所有可用角色
    allRoles.value = Array.isArray(allRolesResponse.data)
      ? allRolesResponse.data
      : [];

    // 重置选中的角色ID
    selectedRoleId.value = undefined;

    console.log("用户当前角色:", currentUserRoles.value);
    console.log("所有可用角色:", allRoles.value);
  } catch (error) {
    console.error("获取角色数据失败:", error);
    ElMessage.error("获取角色数据失败，请稍后重试");
    bindRoleDialogVisible.value = false;
  } finally {
    bindRoleLoading.value = false;
  }
};

// 提交角色绑定
const handleSubmitBindRole = async () => {
  if (!currentUser.value || !selectedRoleId.value) {
    ElMessage.warning("请选择要绑定的角色");
    return;
  }

  try {
    bindRoleLoading.value = true;

    await bindUserRoleApi({
      userId: currentUser.value.id,
      roleId: selectedRoleId.value,
    });

    ElMessage.success("角色绑定成功");
    bindRoleDialogVisible.value = false;

    // 刷新用户列表
    loadUserData();
  } catch (error) {
    console.error("角色绑定失败:", error);
    ElMessage.error("角色绑定失败，请稍后重试");
  } finally {
    bindRoleLoading.value = false;
  }
};

// 取消角色绑定
const handleCancelBindRole = () => {
  bindRoleDialogVisible.value = false;
  currentUser.value = null;
  currentUserRoles.value = [];
  allRoles.value = [];
  selectedRoleId.value = undefined;
};

// 输入处理函数
const handleUsernameInput = () => {
  // 处理用户名输入
};

const handleNicknameInput = () => {
  // 处理昵称输入
};

const handlePhoneInput = () => {
  // 处理手机号输入
};

const handleEmailInput = () => {
  // 处理邮箱输入
};

// 字符串清理函数
const cleanString = (str: string | undefined): string => {
  if (!str) return '';
  return str.trim();
};

const cleanPhoneNumber = (phone: string | undefined): string => {
  if (!phone) return '';
  // 移除非数字字符
  return phone.replace(/\D/g, '');
};

onMounted(() => {
  // 组件挂载后加载用户数据
  loadUserData();
});
</script>

<template>
  <div class="user-management">
    <!-- 搜索区域 -->

    <!-- 搜索区域 - 修改后的模板 -->
    <div class="search-section">
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名称">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名称"
              clearable
              style="width: 200px"
              @input="handleUsernameInput"
              @blur="
                searchForm.username = cleanString(searchForm.username) || ''
              "
            />
          </el-form-item>
          <el-form-item label="用户昵称">
            <el-input
              v-model="searchForm.nickname"
              placeholder="请输入用户昵称"
              clearable
              style="width: 200px"
              @input="handleNicknameInput"
              @blur="
                searchForm.nickname = cleanString(searchForm.nickname) || ''
              "
            />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入手机号码"
              clearable
              style="width: 200px"
              @input="handlePhoneInput"
              @blur="
                searchForm.phone = cleanPhoneNumber(searchForm.phone) || ''
              "
            />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input
              v-model="searchForm.email"
              placeholder="请输入邮箱"
              clearable
              style="width: 200px"
              @input="handleEmailInput"
              @blur="searchForm.email = cleanString(searchForm.email) || ''"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <vxe-grid ref="xGridDom" v-bind="xGridOpt">
          <!-- 工具栏按钮 -->
          <template #toolbar-btns>
            <vxe-button status="primary" icon="vxe-icon-add" @click="handleAdd">
              新增用户
            </vxe-button>
            <vxe-button
              status="danger"
              icon="vxe-icon-delete"
              @click="handleBatchDelete"
            >
              批量删除
            </vxe-button>
          </template>

          <!-- 性别列 -->
          <template #gender-column="{ row, column }">
            <el-tag
              :type="getGenderTagType(row[column.field])"
              effect="light"
              size="small"
            >
              {{ row[column.field] }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action-column="{ row }">
            <el-button
              link
              type="success"
              size="small"
              @click="handleBindRole(row)"
            >
              绑定角色
            </el-button>
          </template>
        </vxe-grid>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="pagination.pageSizes"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 绑定角色对话框 -->
    <el-dialog
      v-model="bindRoleDialogVisible"
      title="绑定角色"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-loading="bindRoleLoading">
        <div class="bind-role-content">
          <!-- 用户信息 -->
          <div class="user-info">
            <h4>用户信息</h4>
            <p>
              <strong>用户名：</strong>{{ currentUser?.username }}
              <span style="margin-left: 20px">
                <strong>昵称：</strong>{{ currentUser?.nickname }}
              </span>
            </p>
          </div>

          <!-- 角色选择 -->
          <div class="role-selection">
            <h4>选择角色</h4>
            <el-select
              v-model="selectedRoleId"
              :placeholder="getCurrentRolesPlaceholder()"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="role in allRoles"
                :key="role.id"
                :label="`${role.name} (${role.code})`"
                :value="role.id"
                :disabled="isRoleDisabled(role.id)"
              />
            </el-select>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelBindRole">取消</el-button>
          <el-button
            type="primary"
            :loading="bindRoleLoading"
            @click="handleSubmitBindRole"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.user-management {
  padding: 20px;

  .search-section {
    margin-bottom: 20px;

    .search-card {
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }

  .table-section {
    .table-card {
      :deep(.el-card__body) {
        padding: 0;
      }
    }

    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
    }
  }
}

.assign-role-content {
  .user-info,
  .current-roles,
  .role-selection {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 5px 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .role-tags {
    min-height: 32px;
  }

  .no-roles {
    padding: 8px 0;
  }
}

.bind-role-content {
  .user-info,
  .current-roles,
  .role-selection {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 5px 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .role-tags {
    min-height: 32px;
  }

  .no-roles {
    padding: 8px 0;
  }
}

.dialog-footer {
  text-align: right;
}
</style>