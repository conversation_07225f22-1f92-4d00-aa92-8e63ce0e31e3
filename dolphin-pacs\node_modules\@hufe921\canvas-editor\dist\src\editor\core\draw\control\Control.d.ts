import { ControlState } from '../../../dataset/enum/Control';
import { IControl, IControlChangeOption, IControlContext, IControlHighlight, IControlInitOption, IControlInstance, IDestroyControlOption, IGetControlValueOption, IGetControlValueResult, IInitNextControlOption, INextControlContext, IRepaintControlOption, ISetControlExtensionOption, ISetControlProperties, ISetControlRowFlexOption, ISetControlValueOption } from '../../../interface/Control';
import { IElement, IElementPosition } from '../../../interface/Element';
import { IRange } from '../../../interface/Range';
import { Draw } from '../Draw';
interface IMoveCursorResult {
    newIndex: number;
    newElement: IElement;
}
export declare class Control {
    private controlBorder;
    private draw;
    private range;
    private listener;
    private eventBus;
    private controlSearch;
    private options;
    private controlOptions;
    private activeControl;
    private activeControlValue;
    private preElement;
    constructor(draw: Draw);
    setHighlightList(payload: IControlHighlight[]): void;
    computeHighlightList(): void;
    renderHighlightList(ctx: CanvasRenderingContext2D, pageNo: number): void;
    getDraw(): Draw;
    filterAssistElement(elementList: IElement[]): IElement[];
    getIsRangeCanCaptureEvent(): boolean;
    getIsRangeInPostfix(): boolean;
    getIsRangeWithinControl(): boolean;
    getIsElementListContainFullControl(elementList: IElement[]): boolean;
    getIsDisabledControl(context?: IControlContext): boolean;
    getIsDisabledPasteControl(context?: IControlContext): boolean;
    getIsExistValueByElementListIndex(elementList: IElement[], index: number): boolean;
    getControlHighlight(elementList: IElement[], index: number): string;
    getContainer(): HTMLDivElement;
    getElementList(): IElement[];
    getPosition(): IElementPosition | null;
    getPreY(): number;
    getRange(): IRange;
    shrinkBoundary(context?: IControlContext): void;
    getActiveControl(): IControlInstance | null;
    getControlElementList(context?: IControlContext): IElement[];
    updateActiveControlValue(): void;
    emitControlChange(state: ControlState): void;
    initControl(): void;
    destroyControl(options?: IDestroyControlOption): void;
    repaintControl(options?: IRepaintControlOption): void;
    emitControlContentChange(options?: IControlChangeOption): void;
    reAwakeControl(): void;
    moveCursor(position: IControlInitOption): IMoveCursorResult;
    removeControl(startIndex: number, context?: IControlContext): number | null;
    removePlaceholder(startIndex: number, context?: IControlContext): void;
    addPlaceholder(startIndex: number, context?: IControlContext): void;
    setValue(data: IElement[]): number;
    setControlProperties(properties: Partial<IControl>, context?: IControlContext): void;
    keydown(evt: KeyboardEvent): number | null;
    cut(): number;
    getValueById(payload: IGetControlValueOption): IGetControlValueResult;
    setValueListById(payload: ISetControlValueOption[]): void;
    setExtensionListById(payload: ISetControlExtensionOption[]): void;
    setPropertiesListById(payload: ISetControlProperties[]): void;
    getList(): IElement[];
    recordBorderInfo(x: number, y: number, width: number, height: number): void;
    drawBorder(ctx: CanvasRenderingContext2D): void;
    getPreControlContext(): INextControlContext | null;
    getNextControlContext(): INextControlContext | null;
    initNextControl(option?: IInitNextControlOption): void;
    setMinWidthControlInfo(option: ISetControlRowFlexOption): void;
}
export {};
