import { IAppendElementListOption, IComputeRowListPayload, IDrawOption, IDrawRowPayload, IGetImageOption, IGetOriginValueOption, IGetValueOption, IPainterOption } from '../../interface/Draw';
import { IEditorData, IEditorOption, IEditorResult, ISetValueOption } from '../../interface/Editor';
import { IElement, IElementStyle, ISpliceElementListOption, IInsertElementListOption } from '../../interface/Element';
import { IRow } from '../../interface/Row';
import { Cursor } from '../cursor/Cursor';
import { CanvasEvent } from '../event/CanvasEvent';
import { GlobalEvent } from '../event/GlobalEvent';
import { HistoryManager } from '../history/HistoryManager';
import { Listener } from '../listener/Listener';
import { Position } from '../position/Position';
import { RangeManager } from '../range/RangeManager';
import { Search } from './interactive/Search';
import { ImageParticle } from './particle/ImageParticle';
import { TextParticle } from './particle/TextParticle';
import { TableParticle } from './particle/table/TableParticle';
import { TableTool } from './particle/table/TableTool';
import { HyperlinkParticle } from './particle/HyperlinkParticle';
import { Header } from './frame/Header';
import { EditorMode, PageMode, PaperDirection } from '../../dataset/enum/Editor';
import { Control } from './control/Control';
import { CheckboxParticle } from './particle/CheckboxParticle';
import { RadioParticle } from './particle/RadioParticle';
import { DeepRequired, IPadding } from '../../interface/Common';
import { WorkerManager } from '../worker/WorkerManager';
import { Previewer } from './particle/previewer/Previewer';
import { DateParticle } from './particle/date/DateParticle';
import { IMargin } from '../../interface/Margin';
import { I18n } from '../i18n/I18n';
import { ImageObserver } from '../observer/ImageObserver';
import { Zone } from '../zone/Zone';
import { Footer } from './frame/Footer';
import { ListParticle } from './particle/ListParticle';
import { EventBus } from '../event/eventbus/EventBus';
import { EventBusMap } from '../../interface/EventBus';
import { Group } from './interactive/Group';
import { Override } from '../override/Override';
import { LineBreakParticle } from './particle/LineBreakParticle';
import { ITd } from '../../interface/table/Td';
import { TableOperate } from './particle/table/TableOperate';
import { Area } from './interactive/Area';
import { Badge } from './frame/Badge';
export declare class Draw {
    private container;
    private pageContainer;
    private pageList;
    private ctxList;
    private pageNo;
    private renderCount;
    private pagePixelRatio;
    private mode;
    private options;
    private position;
    private zone;
    private elementList;
    private listener;
    private eventBus;
    private override;
    private i18n;
    private canvasEvent;
    private globalEvent;
    private cursor;
    private range;
    private margin;
    private background;
    private badge;
    private search;
    private group;
    private area;
    private underline;
    private strikeout;
    private highlight;
    private historyManager;
    private previewer;
    private imageParticle;
    private laTexParticle;
    private textParticle;
    private tableParticle;
    private tableTool;
    private tableOperate;
    private pageNumber;
    private lineNumber;
    private waterMark;
    private placeholder;
    private header;
    private footer;
    private hyperlinkParticle;
    private dateParticle;
    private separatorParticle;
    private pageBreakParticle;
    private superscriptParticle;
    private subscriptParticle;
    private checkboxParticle;
    private radioParticle;
    private blockParticle;
    private listParticle;
    private lineBreakParticle;
    private control;
    private pageBorder;
    private workerManager;
    private scrollObserver;
    private selectionObserver;
    private imageObserver;
    private LETTER_REG;
    private WORD_LIKE_REG;
    private rowList;
    private pageRowList;
    private painterStyle;
    private painterOptions;
    private visiblePageNoList;
    private intersectionPageNo;
    private lazyRenderIntersectionObserver;
    private printModeData;
    constructor(rootContainer: HTMLElement, options: DeepRequired<IEditorOption>, data: IEditorData, listener: Listener, eventBus: EventBus<EventBusMap>, override: Override);
    setPrintData(): void;
    clearPrintData(): void;
    getLetterReg(): RegExp;
    getMode(): EditorMode;
    setMode(payload: EditorMode): void;
    isReadonly(): boolean;
    isDisabled(): boolean;
    isDesignMode(): boolean;
    isPrintMode(): boolean;
    getOriginalWidth(): number;
    getOriginalHeight(): number;
    getWidth(): number;
    getHeight(): number;
    getMainHeight(): number;
    getMainOuterHeight(): number;
    getCanvasWidth(pageNo?: number): number;
    getCanvasHeight(pageNo?: number): number;
    getInnerWidth(): number;
    getOriginalInnerWidth(): number;
    getContextInnerWidth(): number;
    getMargins(): IMargin;
    getOriginalMargins(): number[];
    getPageGap(): number;
    getOriginalPageGap(): number;
    getPageNumberBottom(): number;
    getMarginIndicatorSize(): number;
    getDefaultBasicRowMarginHeight(): number;
    getHighlightMarginHeight(): number;
    getTdPadding(): IPadding;
    getContainer(): HTMLDivElement;
    getPageContainer(): HTMLDivElement;
    getVisiblePageNoList(): number[];
    setVisiblePageNoList(payload: number[]): void;
    getIntersectionPageNo(): number;
    setIntersectionPageNo(payload: number): void;
    getPageNo(): number;
    setPageNo(payload: number): void;
    getRenderCount(): number;
    getPage(pageNo?: number): HTMLCanvasElement;
    getPageList(): HTMLCanvasElement[];
    getPageCount(): number;
    getTableRowList(sourceElementList: IElement[]): IRow[];
    getOriginalRowList(): IRow[];
    getRowList(): IRow[];
    getPageRowList(): IRow[][];
    getCtx(): CanvasRenderingContext2D;
    getOptions(): DeepRequired<IEditorOption>;
    getSearch(): Search;
    getGroup(): Group;
    getArea(): Area;
    getBadge(): Badge;
    getHistoryManager(): HistoryManager;
    getPosition(): Position;
    getZone(): Zone;
    getRange(): RangeManager;
    getLineBreakParticle(): LineBreakParticle;
    getTextParticle(): TextParticle;
    getHeaderElementList(): IElement[];
    getTableElementList(sourceElementList: IElement[]): IElement[];
    getElementList(): IElement[];
    getMainElementList(): IElement[];
    getOriginalElementList(): IElement[];
    getOriginalMainElementList(): IElement[];
    getFooterElementList(): IElement[];
    getTd(): ITd | null;
    insertElementList(payload: IElement[], options?: IInsertElementListOption): void;
    appendElementList(elementList: IElement[], options?: IAppendElementListOption): void;
    spliceElementList(elementList: IElement[], start: number, deleteCount: number, items?: IElement[], options?: ISpliceElementListOption): void;
    getCanvasEvent(): CanvasEvent;
    getGlobalEvent(): GlobalEvent;
    getListener(): Listener;
    getEventBus(): EventBus<EventBusMap>;
    getOverride(): Override;
    getCursor(): Cursor;
    getPreviewer(): Previewer;
    getImageParticle(): ImageParticle;
    getTableTool(): TableTool;
    getTableOperate(): TableOperate;
    getTableParticle(): TableParticle;
    getHeader(): Header;
    getFooter(): Footer;
    getHyperlinkParticle(): HyperlinkParticle;
    getDateParticle(): DateParticle;
    getListParticle(): ListParticle;
    getCheckboxParticle(): CheckboxParticle;
    getRadioParticle(): RadioParticle;
    getControl(): Control;
    getWorkerManager(): WorkerManager;
    getImageObserver(): ImageObserver;
    getI18n(): I18n;
    getRowCount(): number;
    getDataURL(payload?: IGetImageOption): Promise<string[]>;
    getPainterStyle(): IElementStyle | null;
    getPainterOptions(): IPainterOption | null;
    setPainterStyle(payload: IElementStyle | null, options?: IPainterOption): void;
    setDefaultRange(): void;
    getIsPagingMode(): boolean;
    setPageMode(payload: PageMode): void;
    setPageScale(payload: number): void;
    getPagePixelRatio(): number;
    setPagePixelRatio(payload: number | null): void;
    setPageDevicePixel(): void;
    setPaperSize(width: number, height: number): void;
    setPaperDirection(payload: PaperDirection): void;
    setPaperMargin(payload: IMargin): void;
    getOriginValue(options?: IGetOriginValueOption): Required<IEditorData>;
    getValue(options?: IGetValueOption): IEditorResult;
    setValue(payload: Partial<IEditorData>, options?: ISetValueOption): void;
    setEditorData(payload: Partial<IEditorData>): void;
    private _wrapContainer;
    private _formatContainer;
    private _createPageContainer;
    private _createPage;
    private _initPageContext;
    getElementFont(el: IElement, scale?: number): string;
    getElementSize(el: IElement): number;
    getElementRowMargin(el: IElement): number;
    computeRowList(payload: IComputeRowListPayload): IRow[];
    private _computePageList;
    private _drawHighlight;
    drawRow(ctx: CanvasRenderingContext2D, payload: IDrawRowPayload): void;
    private _drawFloat;
    private _clearPage;
    private _drawPage;
    private _disconnectLazyRender;
    private _lazyRender;
    private _immediateRender;
    render(payload?: IDrawOption): void;
    setCursor(curIndex: number | undefined): number | undefined;
    submitHistory(curIndex: number | undefined): void;
    destroy(): void;
    clearSideEffect(): void;
}
