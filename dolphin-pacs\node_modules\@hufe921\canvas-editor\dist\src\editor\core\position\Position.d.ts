import { IComputePageRowPositionPayload, IComputePageRowPositionResult, IComputeRowPositionPayload, IFloatPosition, IGetFloatPositionByXYPayload, ISetSurroundPositionPayload } from '../../interface/Position';
import { IElement, IElementPosition } from '../../interface/Element';
import { ICurrentPosition, IGetPositionByXYPayload, IPositionContext } from '../../interface/Position';
import { Draw } from '../draw/Draw';
export declare class Position {
    private cursorPosition;
    private positionContext;
    private positionList;
    private floatPositionList;
    private draw;
    private eventBus;
    private options;
    constructor(draw: Draw);
    getFloatPositionList(): IFloatPosition[];
    getTablePositionList(sourceElementList: IElement[]): IElementPosition[];
    getPositionList(): IElementPosition[];
    getMainPositionList(): IElementPosition[];
    getOriginalPositionList(): IElementPosition[];
    getOriginalMainPositionList(): IElementPosition[];
    getSelectionPositionList(): IElementPosition[] | null;
    setPositionList(payload: IElementPosition[]): void;
    setFloatPositionList(payload: IFloatPosition[]): void;
    computePageRowPosition(payload: IComputePageRowPositionPayload): IComputePageRowPositionResult;
    computePositionList(): void;
    computeRowPosition(payload: IComputeRowPositionPayload): IElementPosition[];
    setCursorPosition(position: IElementPosition | null): void;
    getCursorPosition(): IElementPosition | null;
    getPositionContext(): IPositionContext;
    setPositionContext(payload: IPositionContext): void;
    getPositionByXY(payload: IGetPositionByXYPayload): ICurrentPosition;
    getFloatPositionByXY(payload: IGetFloatPositionByXYPayload): ICurrentPosition | void;
    adjustPositionContext(payload: IGetPositionByXYPayload): ICurrentPosition | null;
    setSurroundPosition(payload: ISetSurroundPositionPayload): {
        x: number;
        rowIncreaseWidth: number;
    };
}
