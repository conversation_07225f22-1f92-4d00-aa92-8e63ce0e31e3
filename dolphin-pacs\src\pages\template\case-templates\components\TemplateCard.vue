<template>
  <div class="template-card" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 模板缩略图 -->
      <div class="template-thumbnail">
        <img 
          :src="template.thumbnail" 
          :alt="template.name"
          @error="handleImageError"
        />
        <!-- 分类标签 -->
        <div class="category-tag">
          {{ template.category }}
        </div>
      </div>
      
      <!-- 模板信息 -->
      <div class="template-info">
        <h3 class="template-name">{{ template.name }}</h3>
        <p class="template-description">{{ template.description }}</p>
      </div>
    </div>
    
    <!-- 悬浮按钮组 -->
    <transition name="fade">
      <div v-show="isHovered" class="action-buttons">
        <el-button 
          type="primary" 
          :icon="View" 
          @click="handlePreview"
          class="action-btn preview-btn"
        >
          预览
        </el-button>
        <el-button 
          type="success" 
          :icon="Download" 
          @click="handleDownload"
          class="action-btn download-btn"
        >
          下载
        </el-button>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { View, Download } from '@element-plus/icons-vue'

defineOptions({
  name: "TemplateCard"
})

// 组件属性
interface Props {
  template: {
    id: string
    name: string
    description: string
    thumbnail: string
    category: string
    downloadUrl?: string
  }
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  preview: [template: Props['template']]
  download: [template: Props['template']]
}>()

// 悬浮状态
const isHovered = ref(false)

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/templates/default-template.jpg' // 默认图片
}

// 预览处理
const handlePreview = () => {
  emit('preview', props.template)
}

// 下载处理
const handleDownload = () => {
  emit('download', props.template)
}
</script>

<style lang="scss" scoped>
.template-card {
  width: 200px;
  height: 300px;
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.template-thumbnail {
  height: 180px;
  position: relative;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .category-tag {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(64, 158, 255, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
}

.template-info {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  
  .template-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .template-description {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    flex: 1;
  }
}

.action-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4));
  padding: 16px;
  display: flex;
  gap: 8px;
  justify-content: center;
  
  .action-btn {
    flex: 1;
    height: 32px;
    font-size: 12px;
    border-radius: 6px;
    
    &.preview-btn {
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
      
      &:hover {
        background: var(--el-color-primary-light-3);
        border-color: var(--el-color-primary-light-3);
      }
    }
    
    &.download-btn {
      background: var(--el-color-success);
      border-color: var(--el-color-success);
      
      &:hover {
        background: var(--el-color-success-light-3);
        border-color: var(--el-color-success-light-3);
      }
    }
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 悬浮时图片缩放效果
.template-card:hover .template-thumbnail img {
  transform: scale(1.05);
}

// 响应式适配
@media screen and (max-width: 480px) {
  .template-card {
    width: 160px;
    height: 240px;
  }
  
  .template-thumbnail {
    height: 140px;
  }
  
  .template-info {
    padding: 12px;
    
    .template-name {
      font-size: 14px;
    }
    
    .template-description {
      font-size: 11px;
    }
  }
  
  .action-buttons {
    padding: 12px;
    
    .action-btn {
      height: 28px;
      font-size: 11px;
    }
  }
}
</style>
