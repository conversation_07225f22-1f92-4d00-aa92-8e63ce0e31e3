export declare enum EditorComponent {
    COMPONENT = "component",
    MENU = "menu",
    MAIN = "main",
    FOOTER = "footer",
    CONTEXTMENU = "contextmenu",
    POPUP = "popup",
    CATALOG = "catalog",
    COMMENT = "comment"
}
export declare enum EditorContext {
    PAGE = "page",
    TABLE = "table"
}
export declare enum EditorMode {
    EDIT = "edit",
    CLEAN = "clean",
    READONLY = "readonly",
    FORM = "form",
    PRINT = "print",
    DESIGN = "design"
}
export declare enum EditorZone {
    HEADER = "header",
    MAIN = "main",
    FOOTER = "footer"
}
export declare enum PageMode {
    PAGING = "paging",
    CONTINUITY = "continuity"
}
export declare enum PaperDirection {
    VERTICAL = "vertical",
    HORIZONTAL = "horizontal"
}
export declare enum WordBreak {
    BREAK_ALL = "break-all",
    BREAK_WORD = "break-word"
}
export declare enum RenderMode {
    SPEED = "speed",
    COMPATIBILITY = "compatibility"
}
