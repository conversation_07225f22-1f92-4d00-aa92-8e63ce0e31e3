export declare enum ControlType {
    TEXT = "text",
    SELECT = "select",
    CHECKBOX = "checkbox",
    RADIO = "radio",
    DATE = "date",
    NUMBER = "number"
}
export declare enum ControlComponent {
    PREFIX = "prefix",
    POSTFIX = "postfix",
    PRE_TEXT = "preText",
    POST_TEXT = "postText",
    PLACEHOLDER = "placeholder",
    VALUE = "value",
    CHECKBOX = "checkbox",
    RADIO = "radio"
}
export declare enum ControlIndentation {
    ROW_START = "rowStart",
    VALUE_START = "valueStart"
}
export declare enum ControlState {
    ACTIVE = "active",
    INACTIVE = "inactive"
}
