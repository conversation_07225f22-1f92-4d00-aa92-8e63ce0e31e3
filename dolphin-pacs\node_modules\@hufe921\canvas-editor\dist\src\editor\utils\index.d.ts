import { IElementFillRect } from '../interface/Element';
export declare function debounce<T extends unknown[]>(func: (...arg: T) => unknown, delay: number): (this: unknown, ...args: T) => void;
export declare function throttle<T extends unknown[]>(func: (...arg: T) => unknown, delay: number): (this: unknown, ...args: T) => void;
export declare function deepCloneOmitKeys<T, K>(obj: T, omitKeys: (keyof K)[]): T;
export declare function deepClone<T>(obj: T): T;
export declare function isBody(node: Element): boolean;
export declare function findParent(node: Element, filterFn: Function, includeSelf: boolean): Element | null;
export declare function getUUID(): string;
export declare function splitText(text: string): string[];
export declare function downloadFile(href: string, fileName: string): void;
export declare function threeClick(dom: HTMLElement, fn: (evt: MouseEvent) => any): void;
export declare function isObject(type: unknown): type is Record<string, unknown>;
export declare function isArray(type: unknown): type is Array<unknown>;
export declare function isNumber(type: unknown): type is number;
export declare function isString(type: unknown): type is string;
export declare function mergeObject<T>(source: T, target: T): T;
export declare function nextTick(fn: Function): void;
export declare function convertNumberToChinese(num: number): string;
export declare function cloneProperty<T>(properties: (keyof T)[], sourceElement: T, targetElement: T): void;
export declare function pickObject<T>(object: T, pickKeys: (keyof T)[]): T;
export declare function omitObject<T>(object: T, omitKeys: (keyof T)[]): T;
export declare function convertStringToBase64(input: string): string;
export declare function findScrollContainer(element: HTMLElement): HTMLElement;
export declare function isArrayEqual(arr1: unknown[], arr2: unknown[]): boolean;
export declare function isObjectEqual(obj1: unknown, obj2: unknown): boolean;
export declare function isRectIntersect(rect1: IElementFillRect, rect2: IElementFillRect): boolean;
export declare function isNonValue(value: unknown): boolean;
export declare function normalizeLineBreak(text: string): string;
