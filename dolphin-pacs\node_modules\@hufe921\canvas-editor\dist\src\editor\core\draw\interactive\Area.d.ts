import { Draw } from '../Draw';
import { IAreaInfo, IGetAreaValueOption, IGetAreaValueResult, IInsertAreaOption, ISetAreaPropertiesOption } from '../../../interface/Area';
import { IRange } from '../../../interface/Range';
import { IElementPosition } from '../../../interface/Element';
export declare class Area {
    private draw;
    private zone;
    private range;
    private position;
    private areaInfoMap;
    constructor(draw: Draw);
    getAreaInfo(): Map<string, IAreaInfo>;
    getActiveAreaId(): string | null;
    getActiveAreaInfo(): IAreaInfo | null;
    isReadonly(): boolean;
    insertArea(payload: IInsertAreaOption): string | null;
    render(ctx: CanvasRenderingContext2D, pageNo: number): void;
    compute(): void;
    getAreaValue(options?: IGetAreaValueOption): IGetAreaValueResult | null;
    getContextByAreaId(areaId: string): {
        range: IRange;
        elementPosition: IElementPosition;
    } | null;
    setAreaProperties(payload: ISetAreaPropertiesOption): void;
}
