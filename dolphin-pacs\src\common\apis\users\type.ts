export interface UserInfo {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 性别 */
  sex: number
  /** 头像 */
  avatar: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email: string
  /** 用户角色 - 默认角色，因为接口暂时不返回 */
  roles?: string[]
}

export type GetCurrentUserResponseData = ApiResponseData<UserInfo>

/** 用户管理列表的用户信息（与接口返回结构匹配） */
export interface UserManagementInfo {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 用户昵称 */
  nickname: string
  /** 性别 */
  gender: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email: string
}

/** 分页数据结构 */
export interface PaginatedData<T> {
  records: T[]
  total: number
  list?: T[]
  count?: number
}

/** 用户列表API响应 */
export interface UserListApiResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 用户列表数据 - 支持多种格式 */
  data: UserManagementInfo[] | PaginatedData<UserManagementInfo>
}

/** 用户搜索参数 */
export interface UserSearchParams {
  username?: string;
  nickname?: string;
  phone?: string;
  email?: string;
  pageNumber?: number;  // 页码
  pageSize?: number;    // 每页大小
}
