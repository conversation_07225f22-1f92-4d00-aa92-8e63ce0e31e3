import { IControlContext } from '../../interface/Control';
import { IElement } from '../../interface/Element';
import { IRange, IRangeElementStyle, IRangeParagraphInfo, RangeRowArray, RangeRowMap } from '../../interface/Range';
import { Draw } from '../draw/Draw';
export declare class RangeManager {
    private draw;
    private options;
    private range;
    private listener;
    private eventBus;
    private position;
    private historyManager;
    private defaultStyle;
    constructor(draw: Draw);
    getRange(): IRange;
    clearRange(): void;
    setDefaultStyle(style: IRangeElementStyle | null): void;
    getDefaultStyle(): IRangeElementStyle | null;
    getRangeAnchorStyle(elementList: IElement[], anchorIndex: number): IElement | null;
    getIsRangeChange(startIndex: number, endIndex: number, tableId?: string, startTdIndex?: number, endTdIndex?: number, startTrIndex?: number, endTrIndex?: number): boolean;
    getIsCollapsed(): boolean;
    getIsSelection(): boolean;
    getSelection(): IElement[] | null;
    getSelectionElementList(): IElement[] | null;
    getTextLikeSelection(): IElement[] | null;
    getTextLikeSelectionElementList(): IElement[] | null;
    getRangeRow(): RangeRowMap | null;
    getRangeRowElementList(): IElement[] | null;
    getRangeParagraph(): RangeRowArray | null;
    getRangeParagraphInfo(): IRangeParagraphInfo | null;
    getRangeParagraphElementList(): IElement[] | null;
    getRangeTableElement(): IElement | null;
    getIsSelectAll(): boolean;
    getIsPointInRange(x: number, y: number): boolean;
    getKeywordRangeList(payload: string): IRange[];
    getIsCanInput(): boolean;
    setRange(startIndex: number, endIndex: number, tableId?: string, startTdIndex?: number, endTdIndex?: number, startTrIndex?: number, endTrIndex?: number): void;
    replaceRange(range: IRange): void;
    shrinkRange(): void;
    setRangeStyle(): void;
    recoveryRangeStyle(): void;
    shrinkBoundary(context?: IControlContext): void;
    render(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number): void;
    toString(): string;
}
