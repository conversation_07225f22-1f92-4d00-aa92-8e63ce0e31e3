export declare const NAME_PLACEHOLDER: {
    SELECTED_TEXT: string;
};
export declare const INTERNAL_CONTEXT_MENU_KEY: {
    GLOBAL: {
        CUT: string;
        COPY: string;
        PASTE: string;
        SELECT_ALL: string;
        PRINT: string;
    };
    CONTROL: {
        DELETE: string;
    };
    HYPERLINK: {
        DELETE: string;
        CANCEL: string;
        EDIT: string;
    };
    IMAGE: {
        CHANGE: string;
        SAVE_AS: string;
        TEXT_WRAP: string;
        TEXT_WRAP_EMBED: string;
        TEXT_WRAP_UP_DOWN: string;
        TEXT_WRAP_SURROUND: string;
        TEXT_WRAP_FLOAT_TOP: string;
        TEXT_WRAP_FLOAT_BOTTOM: string;
    };
    TABLE: {
        BORDER: string;
        BORDER_ALL: string;
        BORDER_EMPTY: string;
        BORDER_DASH: string;
        BORDER_EXTERNAL: string;
        BORDER_INTERNAL: string;
        BORDER_TD: string;
        BORDER_TD_TOP: string;
        BORDER_TD_RIGHT: string;
        BORDER_TD_BOTTOM: string;
        BORDER_TD_LEFT: string;
        BORDER_TD_FORWARD: string;
        BORDER_TD_BACK: string;
        VERTICAL_ALIGN: string;
        VERTICAL_ALIGN_TOP: string;
        VERTICAL_ALIGN_MIDDLE: string;
        VERTICAL_ALIGN_BOTTOM: string;
        INSERT_ROW_COL: string;
        INSERT_TOP_ROW: string;
        INSERT_BOTTOM_ROW: string;
        INSERT_LEFT_COL: string;
        INSERT_RIGHT_COL: string;
        DELETE_ROW_COL: string;
        DELETE_ROW: string;
        DELETE_COL: string;
        DELETE_TABLE: string;
        MERGE_CELL: string;
        CANCEL_MERGE_CELL: string;
    };
};
