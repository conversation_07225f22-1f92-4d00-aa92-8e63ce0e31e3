import { LocationPosition } from '../dataset/enum/Common';
import { RowFlex } from '../dataset/enum/Row';
import { DeepRequired } from '../interface/Common';
import { IEditorOption } from '../interface/Editor';
import { IElement } from '../interface/Element';
export declare function unzipElementList(elementList: IElement[]): IElement[];
interface IFormatElementListOption {
    isHandleFirstElement?: boolean;
    isForceCompensation?: boolean;
    editorOptions: DeepRequired<IEditorOption>;
}
export declare function formatElementList(elementList: IElement[], options: IFormatElementListOption): void;
export declare function isSameElementExceptValue(source: IElement, target: IElement): boolean;
interface IPickElementOption {
    extraPickAttrs?: Array<keyof IElement>;
}
export declare function pickElementAttr(payload: IElement, option?: IPickElementOption): IElement;
interface IZipElementListOption {
    extraPickAttrs?: Array<keyof IElement>;
    isClassifyArea?: boolean;
    isClone?: boolean;
}
export declare function zipElementList(payload: IElement[], options?: IZipElementListOption): IElement[];
export declare function convertTextAlignToRowFlex(node: HTMLElement): RowFlex;
export declare function convertRowFlexToTextAlign(rowFlex: RowFlex): RowFlex.LEFT | RowFlex.CENTER | RowFlex.RIGHT | RowFlex.JUSTIFY | "justify";
export declare function convertRowFlexToJustifyContent(rowFlex: RowFlex): "center" | "flex-start" | "flex-end" | "space-between";
export declare function isTextLikeElement(element: IElement): boolean;
export declare function getAnchorElement(elementList: IElement[], anchorIndex: number): IElement | null;
export interface IFormatElementContextOption {
    isBreakWhenWrap?: boolean;
    editorOptions?: DeepRequired<IEditorOption>;
}
export declare function formatElementContext(sourceElementList: IElement[], formatElementList: IElement[], anchorIndex: number, options?: IFormatElementContextOption): void;
export declare function convertElementToDom(element: IElement, options: DeepRequired<IEditorOption>): HTMLElement;
export declare function splitListElement(elementList: IElement[]): Map<number, IElement[]>;
export interface IElementListGroupRowFlex {
    rowFlex: RowFlex | null;
    data: IElement[];
}
export declare function groupElementListByRowFlex(elementList: IElement[]): IElementListGroupRowFlex[];
export declare function createDomFromElementList(elementList: IElement[], options?: IEditorOption): HTMLDivElement;
export declare function convertTextNodeToElement(textNode: Element | Node): IElement | null;
export interface IGetElementListByHTMLOption {
    innerWidth: number;
}
export declare function getElementListByHTML(htmlText: string, options: IGetElementListByHTMLOption): IElement[];
export declare function getTextFromElementList(elementList: IElement[]): string;
export declare function getSlimCloneElementList(elementList: IElement[]): IElement[];
export declare function getIsBlockElement(element?: IElement): boolean;
export declare function replaceHTMLElementTag(oldDom: HTMLElement, tagName: keyof HTMLElementTagNameMap): HTMLElement;
export declare function pickSurroundElementList(elementList: IElement[]): IElement[];
export declare function deleteSurroundElementList(elementList: IElement[], pageNo: number): void;
export declare function getNonHideElementIndex(elementList: IElement[], index: number, position?: LocationPosition): number;
export {};
