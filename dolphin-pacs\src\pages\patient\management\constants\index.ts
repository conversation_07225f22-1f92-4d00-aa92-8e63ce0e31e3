import type { DepartmentOption, StatusOption } from "../types"

/** 科室选项数据 */
export const departmentOptions: DepartmentOption[] = [
  { label: "全部科室", value: "" },
  { label: "检验科", value: "检验科" },
  { label: "超声科", value: "超声科" },
  { label: "心电图室", value: "心电图室" },
  { label: "放射科", value: "放射科" },
  { label: "内镜室", value: "内镜室" },
  { label: "内分泌科", value: "内分泌科" }
]

/** 状态选项数据 */
export const statusOptions: StatusOption[] = [
  { label: "全部状态", value: "", type: "info" },
  { label: "待检查", value: "待检查", type: "warning" },
  { label: "检查中", value: "检查中", type: "primary" },
  { label: "分析中", value: "分析中", type: "info" },
  { label: "已完成", value: "已完成", type: "success" },
  { label: "已取消", value: "已取消", type: "danger" }
]

/** 获取状态对应的标签类型 */
export function getStatusTagType(status: string): "primary" | "success" | "warning" | "danger" | "info" {
  const statusOption = statusOptions.find(option => option.value === status)
  return statusOption?.type || "info"
}
