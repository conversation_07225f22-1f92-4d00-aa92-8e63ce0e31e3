<template>
  <div class="fabric-video-viewer">
    <!-- 顶部控制栏 -->
    <div class="top-controls">
      <div class="connection-controls">
        <button
          @click="connect"
          :disabled="isConnected"
          class="btn btn-connect"
        >
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <polygon points="10,8 16,12 10,16 10,8"/>
          </svg>
          {{ isConnected ? '已连接' : '连接' }}
        </button>
        <button
          @click="disconnect"
          :disabled="!isConnected"
          class="btn btn-disconnect"
        >
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="6" y="6" width="12" height="12"/>
          </svg>
          断开连接
        </button>
      </div>

      <!-- 播放控制 -->
      <div class="playback-controls">
        <button
          @click="togglePause"
          :disabled="!isConnected"
          class="btn btn-pause"
          :class="{ active: isPaused }"
        >
          <svg v-if="!isPaused" class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <rect x="6" y="4" width="4" height="16"/>
            <rect x="14" y="4" width="4" height="16"/>
          </svg>
          <svg v-else class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polygon points="5,3 19,12 5,21 5,3"/>
          </svg>
          {{ isPaused ? '继续' : '暂停' }}
        </button>
        
        <div class="speed-controls">
          <label>播放速度:</label>
          <button
            v-for="speed in playbackSpeeds"
            :key="speed"
            @click="setPlaybackSpeed(speed)"
            class="btn btn-speed"
            :class="{ active: currentSpeed === speed }"
          >
            {{ speed }}x
          </button>
        </div>
      </div>
    </div>
    
    <!-- 主画布区域 -->
    <div class="canvas-container">
      <!-- 状态指示器 -->
      <div class="status-overlay">
        <div class="status-item">
          <span class="status-dot" :class="{ connected: isConnected, disconnected: !isConnected }"></span>
          <span class="status-text">{{ connectionStatus }}</span>
        </div>
        <div class="stats">
          <div class="stat-item">
            <span class="stat-label">帧率:</span>
            <span class="stat-value">{{ frameRate }} FPS</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总帧:</span>
            <span class="stat-value">{{ frameCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">延迟:</span>
            <span class="stat-value">{{ latency }}ms</span>
          </div>
        </div>
      </div>

      <!-- 画布 -->
      <canvas ref="canvasRef"></canvas>
      
      <!-- 加载指示器 -->
      <div v-if="connectionStatus === '连接中...'" class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>连接中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as fabric from 'fabric'

// 接口定义
interface Props {
  websocketUrl?: string
  canvasWidth?: number
  canvasHeight?: number
  imageType?: string
}

interface ExposedMethods {
  connect: () => void
  disconnect: () => void
  togglePause: () => void
  setPlaybackSpeed: (speed: number) => void
  isConnected: Readonly<Ref<boolean>>
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  websocketUrl: 'ws://*************:14580/ws/video',
  canvasWidth: 800,
  canvasHeight: 600,
  imageType: 'image/jpeg'
})

// 响应式数据
const canvasRef = ref<HTMLCanvasElement | null>(null)
const socket = ref<WebSocket | null>(null)
const fabricCanvas = ref<fabric.Canvas | null>(null)
const isConnected = ref<boolean>(false)
const connectionStatus = ref<string>('未连接')
const frameRate = ref<number>(0)
const frameCount = ref<number>(0)
const latency = ref<number>(0)
const isPaused = ref<boolean>(false)
const currentSpeed = ref<number>(1)

// 播放控制
const playbackSpeeds = [0.5, 1, 1.5, 2, 3]
const frameBuffer: ArrayBuffer[] = []
const maxBufferSize = 30 // 最大缓冲帧数

// 性能优化变量
let currentImageObject: fabric.Image | null = null
let frameRateCounter = 0
let frameRateInterval: NodeJS.Timeout | null = null
let lastFrameTime = 0
let frameProcessingQueue: ArrayBuffer[] = []
let isProcessingFrame = false

// 连接状态枚举
enum ConnectionStatus {
  DISCONNECTED = '未连接',
  CONNECTING = '连接中...',
  CONNECTED = '已连接',
  CLOSED = '连接已关闭',
  ERROR = '连接错误'
}

// 初始化 Fabric.js 画布
const initFabricCanvas = async (): Promise<void> => {
  await nextTick()

  if (!canvasRef.value) {
    console.error('Canvas element not found')
    return
  }

  const container = canvasRef.value.parentElement
  if (!container) {
    console.error('Canvas container not found')
    return
  }

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  fabricCanvas.value = new fabric.Canvas(canvasRef.value, {
    width: containerWidth,
    height: containerHeight,
    backgroundColor: '#000000',
    selection: false,
    preserveObjectStacking: true,
    renderOnAddRemove: false, // 性能优化：禁用自动渲染
    skipTargetFind: true, // 性能优化：跳过目标查找
    imageSmoothingEnabled: false // 性能优化：禁用图像平滑
  })

  console.log('✅ Fabric.js 画布初始化成功', `尺寸: ${containerWidth}x${containerHeight}`)
}

// 连接 WebSocket
const connect = (): void => {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    console.log('✅ 已经连接')
    return
  }

  connectionStatus.value = ConnectionStatus.CONNECTING
  resetStats()
  
  socket.value = new WebSocket(props.websocketUrl)
  socket.value.binaryType = 'arraybuffer'

  socket.value.onopen = (): void => {
    console.log('✅ WebSocket 连接成功')
    isConnected.value = true
    connectionStatus.value = ConnectionStatus.CONNECTED
    startFrameRateCounter()
  }

  socket.value.onmessage = async (event: MessageEvent): Promise<void> => {
    if (event.data instanceof ArrayBuffer && !isPaused.value) {
      const currentTime = performance.now()
      
      // 计算延迟（简单估算）
      if (lastFrameTime > 0) {
        latency.value = Math.round(currentTime - lastFrameTime)
      }
      lastFrameTime = currentTime

      // 根据播放速度控制帧处理
      if (shouldProcessFrame()) {
        addToProcessingQueue(event.data)
      }
    }
  }

  socket.value.onclose = (event: CloseEvent): void => {
    console.log('🔌 连接已关闭:', event.code, event.reason)
    isConnected.value = false
    connectionStatus.value = ConnectionStatus.CLOSED
    socket.value = null
    stopFrameRateCounter()
  }

  socket.value.onerror = (error: Event): void => {
    console.error('❌ WebSocket 错误:', error)
    isConnected.value = false
    connectionStatus.value = ConnectionStatus.ERROR
    stopFrameRateCounter()
  }
}

// 播放速度控制变量
let speedFrameCounter = 0
let lastSpeedTime = 0

// 判断是否应该处理当前帧（基于播放速度）
const shouldProcessFrame = (): boolean => {
  const currentTime = performance.now()
  
  // 对于1x速度，正常处理所有帧
  if (currentSpeed.value === 1) {
    return true
  }
  
  // 计算基于时间的帧控制
  const expectedInterval = 1000 / (30 * currentSpeed.value) // 假设基础帧率为30fps
  
  if (currentTime - lastSpeedTime >= expectedInterval) {
    lastSpeedTime = currentTime
    return true
  }
  
  return false
}

// 添加到处理队列
const addToProcessingQueue = (arrayBuffer: ArrayBuffer): void => {
  frameProcessingQueue.push(arrayBuffer)
  processNextFrame()
}

// 处理下一帧
const processNextFrame = async (): Promise<void> => {
  if (isProcessingFrame || frameProcessingQueue.length === 0) {
    return
  }

  isProcessingFrame = true
  const arrayBuffer = frameProcessingQueue.shift()
  
  if (arrayBuffer) {
    await displayFrameOptimized(arrayBuffer)
    updateFrameRate()
    frameCount.value++
  }
  
  isProcessingFrame = false
  
  // 继续处理队列中的下一帧
  if (frameProcessingQueue.length > 0) {
    requestAnimationFrame(() => processNextFrame())
  }
}

// 优化的帧显示函数
const displayFrameOptimized = async (arrayBuffer: ArrayBuffer): Promise<void> => {
  try {
    const blob = new Blob([arrayBuffer], { type: props.imageType })
    const imageBitmap = await createImageBitmap(blob)
    
    if (!fabricCanvas.value) {
      imageBitmap.close()
      return
    }

    const canvasWidth = fabricCanvas.value.width || props.canvasWidth
    const canvasHeight = fabricCanvas.value.height || props.canvasHeight

    // 直接使用 ImageBitmap 创建 fabric.Image，避免额外的 canvas 转换
    const imgElement = new Image()
    imgElement.onload = () => {
      if (!fabricCanvas.value) return

      // 复用现有的图像对象，只更新其源
      if (currentImageObject) {
        // 更新现有图像对象的元素
        currentImageObject.setElement(imgElement)
        currentImageObject.set({
          scaleX: canvasWidth / imgElement.width,
          scaleY: canvasHeight / imgElement.height
        })
      } else {
        // 创建新的图像对象
        currentImageObject = new fabric.Image(imgElement, {
          left: 0,
          top: 0,
          scaleX: canvasWidth / imgElement.width,
          scaleY: canvasHeight / imgElement.height,
          selectable: false,
          evented: false
        })
        fabricCanvas.value.add(currentImageObject)
      }
      
      // 批量渲染
      fabricCanvas.value.renderAll()
    }

    // 创建临时 canvas 并转换为 data URL
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')!
    tempCanvas.width = imageBitmap.width
    tempCanvas.height = imageBitmap.height
    tempCtx.drawImage(imageBitmap, 0, 0)
    imgElement.src = tempCanvas.toDataURL()
    
    imageBitmap.close()
    
  } catch (error) {
    console.error('显示帧失败:', error)
  }
}

// 暂停/继续
const togglePause = (): void => {
  isPaused.value = !isPaused.value
  console.log(isPaused.value ? '⏸️ 已暂停' : '▶️ 继续播放')
}

// 设置播放速度
const setPlaybackSpeed = (speed: number): void => {
  currentSpeed.value = speed
  lastSpeedTime = 0 // 重置速度计时器
  console.log(`⚡ 播放速度设置为: ${speed}x`)
}

// 断开连接
const disconnect = (): void => {
  if (socket.value) {
    socket.value.close()
  }
  stopFrameRateCounter()
  frameProcessingQueue = []
}

// 启动帧率计数器
const startFrameRateCounter = (): void => {
  frameRateInterval = setInterval((): void => {
    frameRate.value = frameRateCounter
    frameRateCounter = 0
  }, 1000)
}

// 停止帧率计数器
const stopFrameRateCounter = (): void => {
  if (frameRateInterval) {
    clearInterval(frameRateInterval)
    frameRateInterval = null
  }
  frameRate.value = 0
  frameRateCounter = 0
}

// 更新帧率
const updateFrameRate = (): void => {
  frameRateCounter++
}

// 重置统计信息
const resetStats = (): void => {
  frameCount.value = 0
  frameRate.value = 0
  frameRateCounter = 0
  latency.value = 0
  lastFrameTime = 0
  lastSpeedTime = 0
  speedFrameCounter = 0
}

// 调整画布大小
const resizeCanvas = (): void => {
  if (!fabricCanvas.value || !canvasRef.value) return

  const container = canvasRef.value.parentElement
  if (!container) return

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  fabricCanvas.value.setDimensions({
    width: containerWidth,
    height: containerHeight
  })

  if (currentImageObject) {
    const imgElement = currentImageObject.getElement() as HTMLImageElement
    if (imgElement) {
      currentImageObject.set({
        scaleX: containerWidth / imgElement.width,
        scaleY: containerHeight / imgElement.height
      })
      fabricCanvas.value.renderAll()
    }
  }
}

// 组件挂载
onMounted((): void => {
  initFabricCanvas()
  window.addEventListener('resize', resizeCanvas)
})

// 组件卸载
onUnmounted((): void => {
  disconnect()
  window.removeEventListener('resize', resizeCanvas)
  if (fabricCanvas.value) {
    fabricCanvas.value.dispose()
  }
})

// 暴露方法给父组件
defineExpose<ExposedMethods>({
  connect,
  disconnect,
  togglePause,
  setPlaybackSpeed,
  isConnected: readonly(isConnected)
})
</script>

<style scoped>
.fabric-video-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-controls,
.playback-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speed-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.speed-controls label {
  font-size: 14px;
  opacity: 0.8;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-connect {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-disconnect {
  background: linear-gradient(135deg, #f44336, #da190b);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn-pause {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-pause.active {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-speed {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
}

.btn-speed.active {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.icon {
  width: 16px;
  height: 16px;
}

.canvas-container {
  flex: 1;
  position: relative;
  margin: 24px;
  border-radius: 16px;
  overflow: hidden;
  background: #000;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.canvas-container canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.status-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s;
}

.status-dot.connected {
  background: #4CAF50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
}

.status-dot.disconnected {
  background: #f44336;
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.6);
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.7;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #4CAF50;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-controls {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }
  
  .speed-controls {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .canvas-container {
    margin: 16px;
  }
  
  .status-overlay {
    top: 10px;
    right: 10px;
    padding: 12px;
  }
}
</style>